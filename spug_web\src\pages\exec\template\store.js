/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import { observable } from "mobx";
import { http, includes } from 'libs';

class Store {
  ParameterTypes = {
    'string': '文本框',
    'password': '密码框',
    'select': '下拉选择'
  }
  @observable records = [];
  @observable types = [];
  @observable record = {parameters: []};
  @observable isFetching = false;
  @observable formVisible = false;

  @observable f_name;
  @observable f_type;

  get dataSource() {
    // 调试语句：检查records的类型和内容
    console.log('[DEBUG] dataSource getter called');
    console.log('[DEBUG] this.records type:', typeof this.records);
    console.log('[DEBUG] this.records isArray:', Array.isArray(this.records));
    console.log('[DEBUG] this.records value:', this.records);

    // 确保records始终是数组
    let data = Array.isArray(this.records) ? this.records : []
    console.log('[DEBUG] data after array check:', data);

    if (this.f_name) data = data.filter(x => includes(x.name, this.f_name))
    if (this.f_type) data = data.filter(x => includes(x.type, this.f_type))

    console.log('[DEBUG] final data:', data);
    return data
  }

  fetchRecords = () => {
    console.log('[DEBUG] fetchRecords called');
    this.isFetching = true;
    http.get('/api/exec/template/')
      .then(response => {
        console.log('[DEBUG] API response received:', response);
        console.log('[DEBUG] response type:', typeof response);
        console.log('[DEBUG] response.types:', response?.types);
        console.log('[DEBUG] response.templates:', response?.templates);

        // 处理响应数据，确保数据类型正确
        const types = response?.types || [];
        const templates = response?.templates || [];

        console.log('[DEBUG] extracted types:', types);
        console.log('[DEBUG] extracted templates:', templates);
        console.log('[DEBUG] templates isArray:', Array.isArray(templates));

        this.records = Array.isArray(templates) ? templates : [];
        this.types = Array.isArray(types) ? types : [];

        console.log('[DEBUG] this.records after assignment:', this.records);
        console.log('[DEBUG] this.types after assignment:', this.types);
      })
      .catch(error => {
        console.error('[DEBUG] 获取模板列表失败:', error);
        this.records = [];
        this.types = [];
      })
      .finally(() => {
        console.log('[DEBUG] fetchRecords finally block');
        this.isFetching = false;
      })
  };

  showForm = (info = {interpreter: 'sh', host_ids: [], parameters: []}) => {
    this.formVisible = true;
    this.record = info
  }
}

export default new Store()
